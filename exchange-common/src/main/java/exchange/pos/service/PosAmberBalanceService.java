package exchange.pos.service;

import java.math.BigDecimal;
import java.net.HttpURLConnection;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import org.apache.commons.codec.digest.HmacUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import exchange.common.http.cb.HttpClient;
import exchange.common.util.JsonUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
@Transactional(readOnly = true, transactionManager = "masterTransactionManager")
@RequiredArgsConstructor
public class PosAmberBalanceService {

  @Value("${exchange-pos.base-trade.amber.api-host:#{null}}")
  private String apiHost;

  @Value("${exchange-pos.base-trade.amber.access-secret:#{null}}")
  private String accessSecret;

  @Value("${exchange-pos.base-trade.amber.access-key:#{null}}")
  private String accessKey;

  private static final String SIGN_ALGORITHM = "HmacSha256";
  private static final String GET_BALANCE_METHOD = "GET";
  private static final int TOO_MANY_REQUESTS = 429;

  public Map<String, BigDecimal> getBalance() {
    String apiPath = "/api/v2/asset/balance";
    long timestamp = System.currentTimeMillis();
    String path = apiPath;
    String signStr = "method=" + GET_BALANCE_METHOD + "&path=" + path + "&timestamp=" + timestamp;

    String sign = new HmacUtils(SIGN_ALGORITHM, accessSecret).hmacHex(signStr);

    ArrayList<String> headers = new ArrayList<>(6);
    headers.add("access-key");
    headers.add(accessKey);
    headers.add("access-timestamp");
    headers.add(String.valueOf(timestamp));
    headers.add("access-sign");
    headers.add(sign);
    log.info("pos amber balance access-key: {} , accessSecret: {}, access-timestamp: {}, sign: {}",accessKey, accessSecret, timestamp, sign);
    String requestUrl = apiHost + path;
    log.info("pos amber request-url:{}",requestUrl);
    HttpClient.HttpResult result = HttpClient.httpGet(requestUrl, headers, Collections.emptyMap());
    log.info("pos amber repo: content: {}, code: {}", result.content, result.code);
    if (HttpURLConnection.HTTP_INTERNAL_ERROR == result.code) {
      log.warn(
          "Failed to request API {}. code: {}, msg: {},Internal Server Error", requestUrl, result.code, result.content);
      return null;
    }
    if (HttpURLConnection.HTTP_BAD_REQUEST == result.code) {
      log.warn(
          "Failed to request API {}. code: {}, msg: {},Bad request/Request failed", requestUrl, result.code, result.content);
      return null;
    }
    if (HttpURLConnection.HTTP_UNAUTHORIZED == result.code) {
      log.warn(
          "Failed to request API {}. code: {}, msg: {},Unauthorize", requestUrl, result.code, result.content);
      return null;
    }
    if (HttpURLConnection.HTTP_FORBIDDEN == result.code) {
      log.warn(
          "Failed to request API {}. code: {}, msg: {},Forbidden/IP restriction", requestUrl, result.code, result.content);
      return null;
    }
    if (HttpURLConnection.HTTP_NOT_FOUND == result.code) {
      log.warn(
          "Failed to request API {}. code: {}, msg: {},Not found", requestUrl, result.code, result.content);
      return null;
    }
    if (HttpURLConnection.HTTP_UNSUPPORTED_TYPE == result.code) {
      log.warn(
          "Failed to request API {}. code: {}, msg: {},Unsupported media type", requestUrl, result.code, result.content);
      return null;
    }
    if (TOO_MANY_REQUESTS == result.code) {
      log.warn(
          "Failed to request API {}. code: {}, msg: {},Too many requests", requestUrl, result.code, result.content);
      return null;
    }
    String responseJson = result.content;
    Map<String, Object> responseObj = JsonUtil.decode(responseJson, Map.class);
    if (CollectionUtils.isEmpty(responseObj)) {
      log.warn("Failed to parse json for get balance of the amber: {}", responseJson);
      return null;
    }
    Object resultObj = responseObj.get("result");
    if (Objects.isNull(resultObj)) {
      log.warn("No result returned from amber: {}", responseJson);
      return null;
    }
    // 解析余额详情
    Map<String, BigDecimal> availableBalanceMap = new HashMap<>();
    if (resultObj instanceof Map balanceObj) {
      ArrayList<Map> balanceDetails = (ArrayList<Map>) balanceObj.get("balanceDetails");
      if (Objects.isNull(balanceDetails)) {
        log.warn("No balance details returned from amber: {}", responseJson);
        return null;
      }
      for(Map balanceDetail : balanceDetails) {
        Object ccy = balanceDetail.get("ccy");
        Object availableBalance = balanceDetail.get("availableBalance");
        if (Objects.isNull(ccy) || Objects.isNull(availableBalance)) {
          log.warn("No available balance returned from amber: {}", responseJson);
          return null;
        }
        availableBalanceMap.put(ccy.toString(), new BigDecimal(availableBalance.toString()));
      }
      
      return availableBalanceMap;
    } else {
      log.warn("The result object does not expected: {}", responseJson);
      return null;
    }
  }
}
